# VERRNN 快速开始指南

## 🎯 概述
本指南将帮助您在10分钟内完成VERRNN项目的环境搭建和基本实验运行。

## 📋 前置要求
- Docker Desktop (已安装并运行)
- IBM CPLEX学术版许可证文件
- 至少8GB可用内存
- macOS或Linux系统

## 🚀 5步快速开始

### 步骤1: 获取项目代码
```bash
# 克隆项目
git clone https://github.com/Gary-oak-Star/VERRNN.git
cd VERRNN
git checkout reproduction-experiment-2025-06-17
```

### 步骤2: 准备CPLEX安装包
```bash
# 将CPLEX安装包复制到项目根目录
# 文件名: cplex_studio221.linux-x86-64.bin
# 确认文件存在
ls -la cplex_studio221.linux-x86-64.bin
```

### 步骤3: 构建Docker环境
```bash
# 构建镜像 (首次需要10-15分钟)
docker build --platform linux/amd64 -t verrnn-image .

# 启动容器
docker run -it --platform linux/amd64 \
  --name verrnn-container \
  -v "$(pwd)":/app/verrnn:ro \
  -v "$(pwd)/data":/app/data \
  verrnn-image
```

### 步骤4: 验证环境
在容器内执行：
```bash
cd /app/data

# 验证CPLEX
python -c "import cplex; print('CPLEX版本:', cplex.__version__)"

# 验证Qhull
/app/qhull/qhull --version

# 验证Python环境
python -c "import numpy, scipy, matplotlib; print('Python环境正常')"
```

### 步骤5: 运行第一个实验
```bash
# 复制测试文件
cp /app/verrnn/pp_test.py .
cp -r /app/verrnn/models .
cp -r /app/verrnn/polytope .

# 运行Polytope Propagation测试
python pp_test.py

# 查看实时输出
tail -f pp_test.log
```

## 🔧 CPLEX在项目中的作用

### 核心功能
CPLEX是IBM的商业线性规划求解器，在VERRNN中承担以下关键任务：

1. **多面体约束求解** (`polytope/polytope.py`)
   - 求解线性不等式系统 Ax ≤ b
   - 计算多面体的顶点表示
   - 判断点是否在多面体内部

2. **固定点计算** (`invariant/invariant.py`)
   - 求解线性规划问题找到不变量
   - 计算RNN状态空间的固定点
   - 优化收敛速度

3. **反例驱动细化** (`cegar/cegar.py`)
   - 求解约束满足问题
   - 生成反例轨迹
   - 细化抽象模型

### 使用场景示例
```python
# 在polytope.py中的典型使用
import cplex

# 创建CPLEX问题实例
prob = cplex.Cplex()

# 添加变量和约束
prob.variables.add(names=['x1', 'x2'])
prob.linear_constraints.add(
    lin_expr=[['x1', 'x2'], [1.0, 1.0]],
    senses=['L'],
    rhs=[1.0]
)

# 求解
prob.solve()
solution = prob.solution.get_values()
```

## 📊 实验类型说明

### 1. Polytope Propagation (pp_test.py)
- **目的:** 验证RNN在给定输入范围内的输出约束
- **方法:** 通过多面体传播计算可达状态集合
- **运行时间:** 5-10分钟
- **输出:** 验证结果(True/False)、多面体数量、计算时间

### 2. Invariant Method (fp_test.py)
- **目的:** 使用固定点方法加速验证过程
- **方法:** 计算归纳不变量，避免完整状态空间探索
- **运行时间:** 2-5分钟
- **输出:** 验证结果、收敛层数、性能对比

### 3. CEGAR Method (cegar_test.py)
- **目的:** 反例驱动的抽象细化验证
- **方法:** 从粗粒度抽象开始，根据反例逐步细化
- **运行时间:** 变化较大，取决于反例复杂度
- **输出:** 验证结果、细化轮数、反例轨迹

### 4. Pulse Test (pulse_test.py)
- **目的:** 测试RNN对脉冲输入的响应特性
- **方法:** 分析特定脉冲模式下的网络行为
- **运行时间:** 1-3分钟
- **输出:** 脉冲响应分析、稳定性评估

## 🎯 预期结果

### 成功指标
- ✅ 所有测试用例正常运行
- ✅ CPLEX求解器无错误
- ✅ 几何计算(Qhull)正常工作
- ✅ 验证结果符合预期

### 性能基准
| 测试类型 | 简单用例 | 中等复杂度 | 高复杂度 |
|---------|----------|------------|----------|
| Polytope | 0.1-0.2秒 | 1-2分钟 | 5-10分钟 |
| Invariant | 0.1-0.2秒 | 30秒-1分钟 | 2-5分钟 |
| CEGAR | 0.5-1秒 | 2-5分钟 | 10-20分钟 |
| Pulse | 0.1-0.5秒 | 1-2分钟 | 3-5分钟 |

## 🔍 故障排除

### 问题1: CPLEX许可证错误
```bash
# 检查许可证文件
ls -la /home/<USER>/ibm/ILOG/CPLEX_Studio221/

# 重新激活许可证
cd /home/<USER>/ibm/ILOG/CPLEX_Studio221/cplex/bin/x86-64_linux/
./cplex
```

### 问题2: 内存不足
```bash
# 监控内存使用
docker stats verrnn-container

# 解决方案：增加Docker内存限制或减少并发测试
```

### 问题3: Qhull路径错误
```bash
# 检查Qhull安装
which qhull
/app/qhull/qhull --version

# 如果错误，检查polytope/pnt2hresp.py第6行路径设置
```

## 📈 下一步

1. **运行完整测试套件**
   ```bash
   # 运行所有测试
   python pp_test.py > pp_test.log 2>&1 &
   python fp_test.py > fp_test.log 2>&1 &
   python cegar_test.py > cegar_test.log 2>&1 &
   python pulse_test.py > pulse_test.log 2>&1 &
   ```

2. **分析结果**
   ```bash
   # 查看所有日志
   grep -E "(True|False|Time)" *.log
   
   # 导出结果
   docker cp verrnn-container:/app/data/ ./results/
   ```

3. **自定义实验**
   - 修改模型参数
   - 调整输入范围
   - 比较不同验证方法的性能

## 📞 获取帮助

- **项目文档:** [REPRODUCTION_REPORT_2025-06-17.md](./REPRODUCTION_REPORT_2025-06-17.md)
- **原始论文:** [VERRNN: Verification of Recurrent Neural Networks](https://arxiv.org/abs/1911.07850)
- **GitHub Issues:** [项目Issues页面](https://github.com/Gary-oak-Star/VERRNN/issues)

---
**预计完成时间:** 15-30分钟（包括Docker构建）
