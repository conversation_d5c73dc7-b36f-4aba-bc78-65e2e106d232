# VERRNN - 复现版本

**循环神经网络验证工具 - 2025年复现实验**

[![Docker](https://img.shields.io/badge/Docker-Ready-blue)](./Dockerfile)
[![CPLEX](https://img.shields.io/badge/CPLEX-22.1.0-green)](./CPLEX_USAGE_GUIDE.md)
[![Python](https://img.shields.io/badge/Python-3.10-yellow)](./requirements.txt)

## 🎯 快速开始

**只需3步，10分钟完成环境搭建和实验运行！**

```bash
# 1. 克隆项目
git clone https://github.com/Gary-oak-Star/VERRNN.git
cd VERRNN && git checkout reproduction-experiment-2025-06-17

# 2. 构建Docker环境 (需要CPLEX安装包)
docker build --platform linux/amd64 -t verrnn-image .

# 3. 运行实验
docker run -it --platform linux/amd64 \
  --name verrnn-container \
  -v "$(pwd)":/app/verrnn:ro \
  -v "$(pwd)/data":/app/data \
  verrnn-image
```

**详细步骤请参考:** [📖 快速开始指南](./QUICK_START_GUIDE.md)

## 📋 项目概述

VERRNN是一个用于认知任务中循环神经网络验证的可达性分析工具，实现了多种RNN验证方法：

- **Polytope Propagation:** 精确的多面体传播验证
- **Invariant Method:** 基于固定点的快速验证
- **CEGAR Method:** 反例驱动的抽象细化
- **Pulse Test:** 脉冲响应特性分析

## 🧪 验证方法对比

| 方法 | 精确度 | 速度 | 适用场景 | 状态 |
|------|--------|------|----------|------|
| Polytope Propagation | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 复杂几何约束 | ✅ 已验证 |
| Invariant Method | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 中等复杂度 | ✅ 已验证 |
| CEGAR Method | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 反例驱动 | 🔄 测试中 |
| Pulse Test | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 脉冲响应 | 🔄 测试中 |

## 🔧 核心技术栈

- **🐳 Docker:** 完整的容器化环境
- **🧮 CPLEX 22.1.0:** IBM线性规划求解器
- **📐 Qhull 2020.2:** 凸包几何计算
- **🐍 Python 3.10:** 核心运行环境
- **📊 NumPy/SciPy:** 数值计算支持

## 📚 文档导航

### 🚀 用户指南
- **[快速开始指南](./QUICK_START_GUIDE.md)** - 10分钟上手教程
- **[CPLEX使用详解](./CPLEX_USAGE_GUIDE.md)** - 求解器详细说明

### 📊 技术文档
- **[复现报告](./REPRODUCTION_REPORT_2025-06-17.md)** - 完整实验结果
- **[Docker配置](./Dockerfile)** - 容器环境配置
- **[依赖列表](./requirements.txt)** - Python包依赖

## 🎯 复现成果

### ✅ 已完成验证
- **Polytope Propagation:** 14个测试用例，支持38层深度验证
- **Invariant Method:** 7个测试用例，67%性能提升
- **环境集成:** CPLEX + Qhull + Docker完美协作

### 📈 性能基准
- **简单用例:** 0.1-0.2秒
- **中等复杂度:** 1-2分钟
- **高复杂度:** 5-10分钟
- **深度验证:** 支持38层RNN网络

## 🔗 相关资源

- **📄 原始论文:** [VERRNN: Verification of Recurrent Neural Networks](https://arxiv.org/abs/1911.07850)
- **🔗 原始仓库:** [nnarodytska/VERRNN](https://github.com/nnarodytska/VERRNN)
- **🍴 Fork仓库:** [Gary-oak-Star/VERRNN](https://github.com/Gary-oak-Star/VERRNN)

## 💡 获取帮助

遇到问题？查看以下资源：

1. **[故障排除指南](./QUICK_START_GUIDE.md#故障排除)**
2. **[CPLEX配置问题](./CPLEX_USAGE_GUIDE.md#故障排除)**
3. **[GitHub Issues](https://github.com/Gary-oak-Star/VERRNN/issues)**

---

**🎉 复现状态:** 主要功能验证成功，核心组件全部正常工作
**📅 复现日期:** 2025年6月17日
**👤 复现人员:** Gary-oak-Star


